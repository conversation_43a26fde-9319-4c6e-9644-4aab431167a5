import { point } from '@flatten-js/core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoRenderElement,
    RenderLine,
    RenderVertex,
    StrokeType,
    RenderSector,
    RenderCircle,
    RenderEllipse,
} from '../model';
import { GeoEpsilon, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vert, vertex, vertexOnStroke, VertexOnStroke } from '../selectors';
import { strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { createFlattenLine } from './util.flatten';
import {
    calculateLineLineIntersection,
    calculateLineCircleIntersection,
    intersectionLineEllipse,
    isPointInSector,
} from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<CommonToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    directionVector: number[] | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.directionVector = undefined;
        super.resetState();
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void;
    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex
    ): any;
    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): any;
    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): any;
    protected abstract getSimpleConstructionLabel(): string;
    protected abstract getComplexConstructionLabel(): string;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line -> Point -> Preview -> Final Point Selection
     */
    protected createSelLogic() {
        // First selector: select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderVector', 'RenderLine', 'RenderLineSegment', 'RenderRay'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: select a point to define through which the line passes
        const firstPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Third selector: enhanced vertex selector with projection for final point on line
        const finalVertexSelector = or(
            [
                // Option 1: Select free vertex with projection onto line
                vertex({
                    preview: true, // Allow selecting preview elements (including first point if it was a preview)
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnLine(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToIntersection(stroke, previewVertex, doc),
                    cfunc: (stroke, doc) => this.checkStrokeIntersection(stroke, doc),
                    refinedFilter: (el: GeoRenderElement) =>
                        isElementLine(el) ||
                        el.type === 'RenderCircle' ||
                        el.type === 'RenderEllipse' ||
                        el.type === 'RenderSector',
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: line -> point -> final vertex
        this.selLogic = then([lineSelector, firstPointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [line, throughPoint, finalVertexSelection] = selector.selected;

                this.selectedLine = strk(line as RenderLine) as RenderLine;
                this.selectedPoint = vert(throughPoint as RenderVertex);
                const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                // Check if final vertex is the same as the through point
                if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                    // If vertices are the same, use simple line construction
                    if (!this.previewLine) {
                        console.error('Preview line not available for same point construction');
                        this.resetState();
                        return;
                    }
                    await this.handleSimpleLineConstruction(doc, this.selectedLine, this.selectedPoint);
                } else {
                    await this.handleComplexLineConstruction(
                        doc,
                        this.selectedLine,
                        this.selectedPoint,
                        finalVertex,
                        finalVertexSelection
                    );
                }
            },
        });
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * or contains the selected point
     */
    protected checkStrokeIntersection(stroke: StrokeType, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false; // No preview line available

        if (strk(stroke).relIndex === this.previewLine.relIndex) return true;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            } else if (stroke.type === 'RenderCircle') {
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            } else if (stroke.type === 'RenderEllipse') {
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            } else if (stroke.type === 'RenderSector') {
                const sector = stroke as RenderSector;

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking stroke intersection:', error);
            return false;
        }
    }

    /**
     * Transform function to project any point onto the line preview
     */
    protected projectOnLine(previewEl: RenderVertex, _doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedLine || !this.selectedPoint || !this.previewLine || !this.directionVector) {
            return previewEl; // Return original if no line is available
        }

        try {
            const throughPointCoords = this.selectedPoint.coords;

            // Check if the preview point is very close to the through point (same point selection)
            const distance = Math.hypot(
                previewEl.coords[0] - throughPointCoords[0],
                previewEl.coords[1] - throughPointCoords[1]
            );

            // If user clicks on the same point as through point, keep it there
            if (distance < GeoEpsilon) {
                previewEl.coords[0] = throughPointCoords[0];
                previewEl.coords[1] = throughPointCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
                return previewEl;
            }

            const projectedCoords = projectPointOntoLine(previewEl.coords, throughPointCoords, this.directionVector);

            if (projectedCoords) {
                previewEl.coords[0] = projectedCoords[0];
                previewEl.coords[1] = projectedCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
            }

            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto line:', error);
            return previewEl;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     */
    protected projectVertexOnStrokeToIntersection(
        stroke: StrokeType,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl
    ): RenderVertex {
        const s = stroke as RenderLine;

        if (this.previewLine.relIndex === s.relIndex) return previewVertex;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            } else if (stroke.type === 'RenderCircle') {
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            } else if (stroke.type === 'RenderEllipse') {
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            } else if (stroke.type === 'RenderSector') {
                // For sector, calculate intersection with underlying circle first
                const sector = stroke as RenderSector;
                const centerCoords = sector.coord('center', doc.rendererCtrl);

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;
                tempCircle.pInfo = { refPEl: [], cCoords: centerCoords };

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            if (intersections?.length) {
                // For multiple intersections (circle, ellipse, sector), find the closest one to current position
                if (
                    intersections.length > 1 &&
                    (stroke.type === 'RenderCircle' ||
                        stroke.type === 'RenderEllipse' ||
                        stroke.type === 'RenderSector')
                ) {
                    const currentPos = point(previewVertex.coords[0], previewVertex.coords[1]);
                    let closestIntersection = intersections[0];
                    let minDistance = currentPos.distanceTo(point(intersections[0].x, intersections[0].y))[0];

                    for (let i = 1; i < intersections.length; i++) {
                        const intersectionPoint = point(intersections[i].x, intersections[i].y);
                        const distance = currentPos.distanceTo(intersectionPoint)[0];

                        if (distance < minDistance) {
                            minDistance = distance;
                            closestIntersection = intersections[i];
                        }
                    }

                    previewVertex.coords = [closestIntersection.x, closestIntersection.y];
                } else {
                    // For single intersection or line intersections, use the first intersection
                    previewVertex.coords = [intersections[0].x, intersections[0].y];
                }
            } else {
                return undefined;
            }
        } catch (error) {
            console.warn('Error projecting vertex on stroke to intersection:', error);
        }

        return previewVertex;
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(this.previewLine.name, line, throughPoint);

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any
    ) {
        try {
            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel()
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;

            // Check if final vertex is from a stroke intersection
            const stroke = strk(finalVertexSelection);
            if (isElementLine(stroke)) {
                // Use intersection construction
                const construction = this.buildLineSegmentWithIntersectionConstruction(
                    combinedName,
                    line,
                    stroke as RenderLine,
                    throughPoint
                );
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            } else {
                // Use segment construction with scaling factor
                const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                let k = 0;
                if (startPt && endPt && this.directionVector) {
                    // Calculate vector from through point to final vertex
                    const toEndVector = [
                        finalVertex.coords[0] - throughPoint.coords[0],
                        finalVertex.coords[1] - throughPoint.coords[1],
                    ];

                    // Calculate dot product to determine direction
                    const dotProduct =
                        toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                    // Distance from through point to final vertex
                    const distance = startPt.distanceTo(endPt)[0];

                    // k is positive if same direction, negative if opposite direction
                    k = dotProduct >= 0 ? distance : -distance;
                }

                const construction = this.buildLineSegmentConstruction(combinedName, line, throughPoint, k);
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel()
                );
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    protected doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const [line, throughPoint, finalSelection] = this.selLogic.selected;

            if (line && throughPoint && !finalSelection) {
                // Second selection: line and point selected, show line preview
                this.selectedLine = line as RenderLine;
                this.selectedPoint = vert(throughPoint as RenderVertex);
                this.createLinePreview(ctrl, this.selectedLine, this.selectedPoint);
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only line selected (not array yet)
            const line = this.selLogic.selected as RenderLine;
            if (
                line &&
                [
                    'RenderLine',
                    'RenderLineSegment',
                    'RenderRay',
                    'RenderCircle',
                    'RenderEllipse',
                    'RenderSector',
                ].includes(line.type)
            ) {
                this.selectedLine = line;
            }
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }
}
