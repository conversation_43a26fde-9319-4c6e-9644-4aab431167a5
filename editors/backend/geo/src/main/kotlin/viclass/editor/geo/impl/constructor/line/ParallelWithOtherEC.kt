package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

@Singleton
class ParallelWithOtherEC : ElementConstructor<LineVi> {

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ThroughPointParallelWithLine, ThroughPointSegmentParallelWithLine, ThroughPointSegmentParallelWithLineAndIntersectionLine
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughPointParallelWithLine.name)
            .hints("LineThroughAPointParallelWithLine")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLine]!!,
                listOf("NameOfLine"),
                "tpl-ParallelWith"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1)
            .elTypes(LineVi::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ThroughPointParallelWithLine -> {
                Validations.validateNumConstraints(c, 2)
                constructParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLine(doc, inputName, c)
            }

            CGS.ThroughPointSegmentParallelWithLineAndIntersectionLine -> {
                Validations.validateNumConstraints(c, 3)
                constructSegmentParallelWithLineAndIntersectionLine(doc, inputName, c)
            }
        }
    }

    private fun findEndingPoint(
        sPointX: Double,
        sPointY: Double,
        uVectorX: Double,
        uVectorY: Double,
        k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    private fun constructSegmentParallelWithLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("not found source line")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("not found point through")
        val k = exr3.result

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)

        val vu = sourceLine.parallelVector.normalize()
        val coords = this.findEndingPoint(
            pointThrough.coordinates().x,
            pointThrough.coordinates().y,
            vu.x,
            vu.y,
            k
        )
        val newPoint = PointImpl(doc, pointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 2,
            paramKind = ParamKind.PK_Value,
            rootPoint = pointThrough.coordinates().toArray(),
            unitVector = vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(pointThrough.coordinates().toArray(), vu.toArray())

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newPoint.name}", pointThrough,
            newPoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(pointThrough, sourceLine), true)

        return cr
    }

    private fun constructSegmentParallelWithLineAndIntersectionLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)

        val sourceLine = exr1.result.result() ?: throw ElementNotExistInDocumentException("not found source line")
        val pointThrough = exr2.result.result() ?: throw ElementNotExistInDocumentException("not found point through")
        val intersectionLine =
            exr3.result.result() ?: throw ElementNotExistInDocumentException("not found intersection line")

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(pointThrough.name)
        }?.first() ?: generatePointName(doc)
        val tempLine = LineImpl(doc, null, pointThrough, sourceLine.parallelVector)
        val intersectionPoint = Intersections.of(intersectionLine, tempLine)
            ?: throw ConstructionException("Invalid line")

        val newpoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        val newLine = LineSegmentImpl(
            doc, "${pointThrough.name}${newpoint.name}", pointThrough,
            newpoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)
        cr.addDependency(newpoint, listOf(pointThrough, sourceLine, intersectionLine), true)

        return cr
    }

    private fun constructParallelWithLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        var exr1: ElementExtraction<Point>? = null
        var exr2: ElementExtraction<LineVi>? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    exr1 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }

                ConstraintParamDefManager.aLine -> {
                    exr2 = extractFirstPossible(doc, ParamKind.PK_Name, p, c.ctIdx)
                }
            }
        }

        val pointThrough = exr1!!.result.result() ?: throw ElementNotExistInDocumentException("Not found through point")
        val sourceLine = exr2!!.result.result() ?: throw ElementNotExistInDocumentException("Not found line")

        val cr = ConstructionResultImpl<LineVi>()

        val lineName: String = if (inputName.isNullOrBlank()) generateLineName(doc) else inputName
        var newLine: LineVi = LineImpl(doc, lineName, pointThrough, sourceLine.parallelVector)

        NamePattern.get(LineSegment::class)!![0].find(lineName)?.let {
            val pName1 = it.groupValues[1]
            val pName2 = it.groupValues[2]

            val p1 = doc.findElementByName(pName1, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Not found through point")
            val p2 = doc.findElementByName(pName2, Point::class, c.ctIdx)
                ?: throw ElementNotExistInDocumentException("Not found through point")

            if (pName1 != pointThrough.name && !newLine.line().contains(p1.coordinates())) {
                throw ConstructionException("point $pName1 is out of target line $lineName")
            }
            if (pName2 != pointThrough.name && !newLine.line().contains(p2.coordinates())) {
                throw ConstructionException("point $pName2 is out of target line $lineName")
            }

            newLine = LineSegmentImpl(doc, lineName, p1, p2)

            cr.addDependency(p1, emptyList(), true)
            cr.addDependency(p2, emptyList(), true)
        }

        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)

        return cr
    }
}
